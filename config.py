import os
from dotenv import load_dotenv

# Загружаем переменные окружения из .env файла
load_dotenv()

# Токен вашего бота в Telegram
TELEGRAM_TOKEN = os.getenv("TELEGRAM_TOKEN")
ADMIN_IDS = [5643973561]

NOTIFICATION_INTERVAL=900
REQUIRED_CHANNEL_ID="@spaacevpn"

# Размер пакета для обработки пользователей из Remnawave
REMNAWAVE_BATCH_SIZE = int(os.getenv("REMNAWAVE_BATCH_SIZE", "100"))

# Platega.io API настройки
# Эти переменные должны быть добавлены в .env файл:
# PLATEGA_API_KEY=<Ваш API ключ>
# PLATEGA_MERCHANT_ID=<Ваш MerchantId>
PLATEGA_API_KEY = os.getenv("PLATEGA_API_KEY")
PLATEGA_MERCHANT_ID = os.getenv("PLATEGA_MERCHANT_ID")