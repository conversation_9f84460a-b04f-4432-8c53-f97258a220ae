import logging
import json
import base64
import requests
import hmac
import hashlib
import os
import uuid
from flask import Flask, request
import asyncio
import httpx
from datetime import datetime, timedelta
import pytz
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.hazmat.primitives.serialization import load_der_public_key
from database import add_balance
from dotenv import load_dotenv

# Настройка логирования
logging.basicConfig(level=logging.INFO)

load_dotenv()

WATA_ACCESS_TOKEN = os.getenv("WATA_ACCESS_TOKEN")
CARD_ACCESS_TOKEN = os.getenv("CARD_ACCESS_TOKEN")
WATA_API_URL = "https://api.wata.pro"
WATA_API_PATH = "/api/h2h"
CRYPTOBOT_TOKEN = os.getenv("CRYPTOBOT_TOKEN")
CRYPTOBOT_API_URL = "https://pay.crypt.bot/api"
MERCHANT_ID = "6f57049b-97f8-4089-b256-dcafec3c357b"
HELEKET_API_KEY = os.getenv("HELEKET_API_KEY")
WEBHOOK_URL = "https://lrtvsk.space/webhook"
PLATEGA_API_KEY = os.getenv("PLATEGA_API_KEY")
PLATEGA_MERCHANT_ID = os.getenv("PLATEGA_MERCHANT_ID")
PLATEGA_API_URL = "https://app.platega.io"

app = Flask(__name__)
HTTP_CLIENT = httpx.AsyncClient(timeout=10)
WATA_PUBLIC_KEY = None

# Получение публичного ключа WATA
def get_wata_public_key():
    global WATA_PUBLIC_KEY
    if WATA_PUBLIC_KEY:
        return WATA_PUBLIC_KEY
    try:
        headers = {"Authorization": f"Bearer {WATA_ACCESS_TOKEN}"}
        url = f"{WATA_API_URL}{WATA_API_PATH}/public-key"
        logging.info(f"Requesting public key from {url}")
        response = requests.get(url, headers=headers, timeout=10)

        if response.status_code != 200:
            logging.error(f"Failed to get Wata public key: Status {response.status_code}, Response: {response.text}")
            return None

        raw_response = response.text
        logging.info(f"Raw response from Wata API: '{raw_response}'")

        try:
            data = response.json()
            public_key_base64 = data.get('value', raw_response)
            logging.info(f"Parsed JSON, public_key_base64: '{public_key_base64[:50]}...'")
        except ValueError:
            public_key_base64 = raw_response.strip()
            logging.info(f"JSON parsing failed, using raw string: '{public_key_base64[:50]}...'")

        public_key_base64 = public_key_base64.replace("-----BEGIN PUBLIC KEY-----", "").replace("-----END PUBLIC KEY-----", "").replace("\n", "").strip()
        padding_needed = len(public_key_base64) % 4
        if padding_needed:
            public_key_base64 += '=' * (4 - padding_needed)
            logging.info(f"Added padding: '{public_key_base64[:50]}...'")

        try:
            public_key_der = base64.b64decode(public_key_base64)
            logging.info(f"Decoded DER bytes (length: {len(public_key_der)}): {public_key_der[:50]}")
            WATA_PUBLIC_KEY = load_der_public_key(public_key_der)
            logging.info("Wata public key loaded successfully as DER")
            return WATA_PUBLIC_KEY
        except base64.binascii.Error as e:
            logging.error(f"Base64 decoding failed: {e}. Input: '{public_key_base64[:50]}...'")
            return None
        except ValueError as e:
            logging.error(f"Failed to load DER key: {e}")
            return None

    except Exception as e:
        logging.error(f"Error fetching Wata public key: {e}")
        return None

# Получение публичного ключа CARD
def get_card_public_key():
    global CARD_PUBLIC_KEY
    if CARD_PUBLIC_KEY:
        return CARD_PUBLIC_KEY
    try:
        headers = {"Authorization": f"Bearer {CARD_ACCESS_TOKEN}"}
        url = f"{WATA_API_URL}{WATA_API_PATH}/public-key"
        logging.info(f"Requesting CARD public key from {url}")
        response = requests.get(url, headers=headers, timeout=10)

        if response.status_code != 200:
            logging.error(f"Failed to get CARD public key: Status {response.status_code}, Response: {response.text}")
            return None

        raw_response = response.text
        logging.info(f"Raw response from CARD API: '{raw_response}'")

        try:
            data = response.json()
            public_key_base64 = data.get('value', raw_response)
            logging.info(f"Parsed JSON, public_key_base64: '{public_key_base64[:50]}...'")
        except ValueError:
            public_key_base64 = raw_response.strip()
            logging.info(f"JSON parsing failed, using raw string: '{public_key_base64[:50]}...'")

        public_key_base64 = public_key_base64.replace("-----BEGIN PUBLIC KEY-----", "").replace("-----END PUBLIC KEY-----", "").replace("\n", "").strip()
        padding_needed = len(public_key_base64) % 4
        if padding_needed:
            public_key_base64 += '=' * (4 - padding_needed)
            logging.info(f"Added padding: '{public_key_base64[:50]}...'")

        try:
            public_key_der = base64.b64decode(public_key_base64)
            logging.info(f"Decoded DER bytes (length: {len(public_key_der)}): {public_key_der[:50]}")
            CARD_PUBLIC_KEY = load_der_public_key(public_key_der)
            logging.info("CARD public key loaded successfully as DER")
            return CARD_PUBLIC_KEY
        except base64.binascii.Error as e:
            logging.error(f"Base64 decoding failed: {e}. Input: '{public_key_base64[:50]}...'")
            return None
        except ValueError as e:
            logging.error(f"Failed to load DER key: {e}")
            return None

    except Exception as e:
        logging.error(f"Error fetching CARD public key: {e}")
        return None

# Проверка подписи WATA
def verify_signature(public_key, signature, data):
    try:
        logging.info(f"Verifying signature. Signature: '{signature}', Data (first 100 chars): '{data[:100]}...'")
        if not hasattr(public_key, 'verify'):
            public_key = load_der_public_key(public_key)
        signature_bytes = base64.b64decode(signature)
        public_key.verify(
            signature_bytes,
            data.encode(),
            padding.PKCS1v15(),
            hashes.SHA512()
        )
        logging.info("Signature verified successfully")
        return True
    except base64.binascii.Error as e:
        logging.error(f"Signature verification failed (Base64 decoding): {e}")
        return False
    except Exception as e:
        logging.error(f"Signature verification failed: {e}")
        return False

# Функции Heleket
def generate_sign(data, api_key):
    """
    Генерация подписи для Heleket API согласно документации:
    MD5 hash of the body of the POST request encoded in base64 and combined with your API key
    """
    # Преобразуем данные в JSON строку БЕЗ сортировки (как отправляем в запросе)
    json_str = json.dumps(data, ensure_ascii=False, separators=(',', ':'))
    
    # Кодируем в base64
    base64_str = base64.b64encode(json_str.encode('utf-8')).decode('ascii')
    
    # Объединяем с API ключом и вычисляем MD5
    to_hash = base64_str + api_key
    signature = hashlib.md5(to_hash.encode('utf-8')).hexdigest()
    
    logging.info(f"JSON string: {json_str}")
    logging.info(f"Base64 string: {base64_str}")
    logging.info(f"String to hash: {to_hash}")
    logging.info(f"Generated signature: {signature}")
    
    return signature

def verify_heleket_webhook_signature(notification, api_key):
    """
    Проверка подписи webhook от Heleket
    """
    if 'sign' not in notification:
        return False
    
    received_sign = notification.pop('sign')  # Убираем sign из данных для проверки
    
    # Генерируем подпись для оставшихся данных
    expected_sign = generate_sign(notification, api_key)
    
    # Возвращаем sign обратно в notification для дальнейшего использования
    notification['sign'] = received_sign
    
    return hmac.compare_digest(expected_sign, received_sign)

# Создание инвойса Heleket
async def create_heleket_invoice(amount, user_id):
    url = "https://api.heleket.com/v1/payment"
    order_id = f"{user_id}heleket{int(datetime.now().timestamp())}"
    
    # Данные запроса
    request_body = {
        "amount": str(amount),
        "currency": "USDT",
        "order_id": order_id,
        "url_callback": WEBHOOK_URL,
        "to_currency": "USDT"
    }
    
    logging.info(f"Request Body: {json.dumps(request_body, indent=2)}")

    # Генерируем подпись согласно документации
    sign = generate_sign(request_body, HELEKET_API_KEY)
    
    # Заголовки согласно документации
    headers = {
        "merchant": MERCHANT_ID,
        "sign": sign,
        "Content-Type": "application/json"
    }
    
    try:
        response = await HTTP_CLIENT.post(url, json=request_body, headers=headers)
        logging.info(f"Heleket Response Status: {response.status_code}")
        logging.info(f"Heleket Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            invoice_data = result.get("result", {})
            pay_url = invoice_data.get("url")
            uuid = invoice_data.get("uuid")
            
            if pay_url and uuid:
                logging.info(f"Heleket invoice created successfully: {uuid}, URL: {pay_url}")
                return pay_url, uuid
            else:
                logging.error(f"Heleket invoice creation failed: url or uuid missing in response: {result}")
                return None, None
        else:
            logging.error(f"Heleket invoice creation failed with status {response.status_code}: {response.text}")
            return None, None
            
    except Exception as e:
        logging.error(f"Error creating Heleket invoice: {e}")
        return None, None

# Создание ссылки WATA
async def create_payment_link(amount, user_id):
    url = f"{WATA_API_URL}{WATA_API_PATH}/links"
    headers = {"Authorization": f"Bearer {WATA_ACCESS_TOKEN}", "Content-Type": "application/json"}
    data = {
        "amount": "{:.2f}".format(amount),
        "currency": "RUB",
        "description": f"Пополнение баланса {user_id}",
        "orderId": f"{user_id}payment{int(datetime.now().timestamp())}",
        "successRedirectUrl": "https://space.lrtvsk.space/",
        "failRedirectUrl": "https://space.lrtvsk.space/"
    }
    response = await HTTP_CLIENT.post(url, json=data, headers=headers)
    return response.json()["url"] if response.status_code == 200 else None

# Создание ссылки CARD
async def create_card_payment_link(amount, user_id):
    url = f"{WATA_API_URL}{WATA_API_PATH}/links"
    headers = {"Authorization": f"Bearer {CARD_ACCESS_TOKEN}", "Content-Type": "application/json"}
    data = {
        "amount": "{:.2f}".format(amount),
        "currency": "RUB",
        "description": f"Пополнение баланса {user_id}",
        "orderId": f"{user_id}payment{int(datetime.now().timestamp())}",
        "successRedirectUrl": "https://space.lrtvsk.space/",
        "failRedirectUrl": "https://space.lrtvsk.space/"
    }
    response = await HTTP_CLIENT.post(url, json=data, headers=headers)
    return response.json()["url"] if response.status_code == 200 else None

# Проверка подписи Crypto Pay
def verify_crypto_signature(body, signature):
    if not CRYPTOBOT_TOKEN:
        logging.error("CRYPTOBOT_TOKEN is not set")
        return False

    secret = hashlib.sha256(CRYPTOBOT_TOKEN.encode()).digest()  # Бинарный SHA256-хэш токена
    computed_hmac = hmac.new(secret, body.encode(), hashlib.sha256).hexdigest()
    logging.info(f"Computed HMAC: {computed_hmac}, Received signature: {signature}")
    return hmac.compare_digest(computed_hmac, signature)

# Создание инвойса Crypto Pay
async def create_crypto_invoice(amount, user_id):
    try:
        if not isinstance(amount, (int, float)) or amount <= 0:
            logging.error(f"Invalid amount for crypto invoice: {amount}")
            return None, None

        url = f"{CRYPTOBOT_API_URL}/createInvoice"
        headers = {
            "Crypto-Pay-API-Token": CRYPTOBOT_TOKEN,
            "Content-Type": "application/json"
        }
        data = {
            "asset": "USDT",
            "amount": str(amount),
            "description": f"Пополнение баланса {user_id}",
            "payload": f"{user_id}_crypto_{int(datetime.now().timestamp())}",
            "paid_btn_name": "viewItem",
            "paid_btn_url": "https://space.lrtvsk.space/",
            "allow_comments": True,
            "allow_anonymous": False
        }

        logging.info(f"Creating crypto invoice with data: {data}")
        response = await HTTP_CLIENT.post(url, json=data, headers=headers)
        response_data = response.json()

        if response.status_code != 200 or not response_data.get("ok"):
            logging.error(f"CryptoPay API error: {response.status_code} - {response_data}")
            return None, None

        result = response_data["result"]
        return result.get("bot_invoice_url"), result.get("invoice_id")

    except Exception as e:
        logging.error(f"Error creating crypto invoice: {e}")
        return None, None

# Функции Platega
def verify_platega_webhook_signature(notification, api_key, merchant_id):
    """
    Проверка подписи webhook от Platega
    Согласно документации, webhook приходит с заголовками X-MerchantId и X-ApiKey
    """
    # В Platega webhook проверяется через заголовки, а не через подпись в теле
    return True  # Проверка будет в handle_webhook через заголовки

# Создание инвойса Platega
async def create_platega_invoice(amount, user_id):
    logging.info(f"Creating Platega invoice for user {user_id}, amount: {amount}")

    # Проверяем наличие необходимых переменных
    if not PLATEGA_API_KEY or not PLATEGA_MERCHANT_ID:
        logging.error("PLATEGA_API_KEY or PLATEGA_MERCHANT_ID not set in environment variables")
        return None, None

    logging.info(f"Platega credentials found - Merchant ID: {PLATEGA_MERCHANT_ID}, API Key: {PLATEGA_API_KEY[:10]}...")

    url = f"{PLATEGA_API_URL}/transaction/process"
    transaction_id = str(uuid.uuid4())  # UUID формат согласно документации

    logging.info(f"Platega URL: {url}")
    logging.info(f"Transaction ID: {transaction_id}")
    
    # Данные запроса согласно документации Platega
    request_body = {
        "paymentMethod": 2,  # СБП / QR согласно документации
        "id": transaction_id,
        "paymentDetails": {
            "amount": int(amount),  # Сумма в рублях согласно документации
            "currency": "RUB"
        },
        "description": f"Пополнение баланса {user_id}",
        "return": "https://space.lrtvsk.space/",
        "failedUrl": "https://space.lrtvsk.space/",
        "payload": f"{user_id}platega{int(datetime.now().timestamp())}"
    }

    # Заголовки согласно документации
    headers = {
        "Content-Type": "application/json",
        "X-MerchantId": PLATEGA_MERCHANT_ID,
        "X-Secret": PLATEGA_API_KEY  # Для создания транзакции используется X-Secret
    }
    
    logging.info(f"Platega Request Body: {json.dumps(request_body, indent=2)}")
    
    try:
        response = await HTTP_CLIENT.post(url, json=request_body, headers=headers)
        logging.info(f"Platega Response Status: {response.status_code}")
        logging.info(f"Platega Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            payment_url = result.get("redirect")
            transaction_id = result.get("transactionId")
            
            if payment_url and transaction_id:
                logging.info(f"Platega invoice created successfully: {transaction_id}, URL: {payment_url}")
                return payment_url, transaction_id
            else:
                logging.error(f"Platega invoice creation failed: redirect or transactionId missing in response: {result}")
                return None, None
        else:
            logging.error(f"Platega invoice creation failed with status {response.status_code}: {response.text}")
            return None, None
            
    except Exception as e:
        logging.error(f"Error creating Platega invoice: {e}")
        return None, None

# Обработка вебхука
@app.route('/webhook', methods=['POST'])
def handle_webhook():
    logging.info("Received webhook")
    logging.info(f"Headers: {dict(request.headers)}")
    raw_body = request.get_data(as_text=True)
    logging.info(f"Raw webhook body: '{raw_body}'")

    # Проверяем, не пустое ли тело
    if not raw_body:
        logging.error("Webhook body is empty")
        return "Empty body", 400

    try:
        notification = json.loads(raw_body)

        # Проверка на Telegram-уведомления (отличаем от CryptoBot по отсутствию update_type)
        if 'update_id' in notification and 'update_type' not in notification:
            logging.info("Received Telegram update, ignoring")
            return "OK", 200  # Просто возвращаем OK для Telegram

        # WATA
        if 'orderId' in notification:
            signature = request.headers.get('X-Signature')
            if not signature:
                logging.error("No X-Signature header provided")
                return "No signature provided", 400
            public_key = get_wata_public_key()
            if not public_key:
                logging.error("Public key not available")
                return "Public key unavailable", 500
            if verify_signature(public_key, signature, raw_body):
                order_id = notification['orderId']
                user_id = int(order_id.split('payment')[0])
                status = notification.get('transactionStatus') or notification.get('status')
                amount = float(notification['amount'])
                if status == 'Paid' and add_balance(user_id, amount, 'sbp', 'RUB'):
                    logging.info(f"Payment processed for user {user_id}: {amount} RUB")
                    return "OK", 200
                else:
                    logging.warning(f"Payment not processed: status={status}")
                    return "Payment not successful", 200
            else:
                logging.warning("Signature verification failed")
                return "Signature invalid", 200

        # CARD
        elif 'orderId' in notification and request.headers.get('X-Signature-Source') == 'CARD':
            signature = request.headers.get('X-Signature')
            if not signature:
                logging.error("No X-Signature header provided for CARD")
                return "No signature provided", 400
            public_key = get_card_public_key()
            if not public_key:
                logging.error("CARD public key not available")
                return "Public key unavailable", 500
            if verify_signature(public_key, signature, raw_body):
                order_id = notification['orderId']
                user_id = int(order_id.split('payment')[0])
                status = notification.get('transactionStatus') or notification.get('status')
                amount = float(notification['amount'])
                if status == 'Paid' and add_balance(user_id, amount, 'sbp', 'RUB'):
                    logging.info(f"CARD payment processed for user {user_id}: {amount} RUB")
                    return "OK", 200
                else:
                    logging.warning(f"CARD payment not processed: status={status}")
                    return "Payment not successful", 200
            else:
                logging.warning("CARD signature verification failed")
                return "Signature invalid", 200

        # Crypto Pay
        elif notification.get("update_type") == "invoice_paid":
            logging.info("Processing CryptoBot invoice_paid webhook")
            signature = request.headers.get("Crypto-Pay-API-Signature")
            if not signature:
                logging.error("No Crypto-Pay-API-Signature header provided")
                return "No signature provided", 400

            logging.info(f"CryptoBot signature received: {signature}")

            # Проверка подписи
            if not verify_crypto_signature(raw_body, signature):
                logging.warning("CryptoBot signature verification failed")
                return "Signature invalid", 400

            logging.info("CryptoBot signature verified successfully")
            
            # Проверка времени запроса (не старше 10 минут)
            request_date_str = notification.get("request_date")
            if request_date_str:
                request_date = datetime.fromisoformat(request_date_str.replace("Z", "+00:00"))
                now = datetime.now(pytz.UTC)
                if now - request_date > timedelta(minutes=10):
                    logging.warning(f"Webhook too old: {request_date_str}")
                    return "Webhook too old", 400
            
            payload = notification["payload"]
            invoice_id = payload.get("invoice_id", "unknown")
            status = payload.get("status", "")
            amount = float(payload.get("amount", 0))
            custom_payload = payload.get("payload", "")

            logging.info(f"CryptoBot webhook: invoice_id={invoice_id}, status={status}, amount={amount}, custom_payload={custom_payload}")

            if status == "paid":
                try:
                    user_id = int(custom_payload.split("_")[0])
                    amount_rub = amount * 90.0
                    logging.info(f"Processing crypto payment for user {user_id}: {amount} USDT -> {amount_rub} RUB")

                    # Добавляем конвертированную сумму к балансу, но записываем оригинальную сумму в историю
                    try:
                        from database import get_db_connection
                        with get_db_connection() as conn:
                            c = conn.cursor()
                            # Обновляем баланс
                            c.execute('UPDATE users SET balance = balance + ? WHERE user_id = ?', (amount_rub, user_id))

                            # Записываем оригинальную сумму в USDT в историю
                            import pytz
                            msk_tz = pytz.timezone('Europe/Moscow')
                            timestamp = datetime.now(msk_tz).isoformat()
                            c.execute('INSERT INTO payment_history (user_id, amount, payment_method, currency, timestamp) VALUES (?, ?, ?, ?, ?)',
                                      (user_id, amount, "crypto", "USDT", timestamp))
                            conn.commit()

                        logging.info(f"Crypto payment processed successfully for user {user_id}: {amount} USDT ({amount_rub} RUB)")
                        return "OK", 200
                    except Exception as e:
                        logging.error(f"Failed to update balance for crypto payment: {e}")
                        return "Failed to update balance", 500
                except (ValueError, IndexError) as e:
                    logging.error(f"Error parsing custom_payload '{custom_payload}': {e}")
                    return "Invalid payload format", 400
            else:
                logging.info(f"CryptoBot payment status is not 'paid': {status}")
                return "OK", 200

        # Heleket
        elif 'uuid' in notification and 'order_id' in notification:
            if not verify_heleket_webhook_signature(notification, HELEKET_API_KEY):
                logging.warning("Heleket signature verification failed")
                return "Signature invalid", 200
            order_id = notification['order_id']
            try:
                user_id = int(order_id.split('heleket')[0])
            except ValueError:
                logging.error(f"Invalid order_id format: {order_id}")
                return "Invalid order_id", 400
            payment_status = notification.get('payment_status')
            if payment_status in ['paid', 'paid_over']:
                amount_usdt = float(notification['amount'])
                amount_rub = amount_usdt * 90.0

                # Добавляем конвертированную сумму к балансу, но записываем оригинальную сумму в историю
                try:
                    from database import get_db_connection
                    with get_db_connection() as conn:
                        c = conn.cursor()
                        # Обновляем баланс
                        c.execute('UPDATE users SET balance = balance + ? WHERE user_id = ?', (amount_rub, user_id))

                        # Записываем оригинальную сумму в USDT в историю
                        import pytz
                        msk_tz = pytz.timezone('Europe/Moscow')
                        timestamp = datetime.now(msk_tz).isoformat()
                        c.execute('INSERT INTO payment_history (user_id, amount, payment_method, currency, timestamp) VALUES (?, ?, ?, ?, ?)',
                                  (user_id, amount_usdt, "heleket", "USDT", timestamp))
                        conn.commit()

                    logging.info(f"Heleket payment processed for user {user_id}: {amount_usdt} USDT ({amount_rub} RUB)")
                    return "OK", 200
                except Exception as e:
                    logging.error(f"Failed to update balance for Heleket payment: {e}")
                    return "Failed to update balance", 500
            else:
                logging.info(f"Heleket payment status: {payment_status} for order {order_id}")
                return "OK", 200

        # Platega
        elif 'id' in notification and 'status' in notification and 'amount' in notification:
            logging.info("Detected Platega webhook format")

            # Проверяем IP адрес отправителя (дополнительная защита)
            client_ip = request.headers.get('X-Real-Ip') or request.headers.get('X-Forwarded-For') or request.remote_addr
            user_agent = request.headers.get('User-Agent', 'Unknown')
            logging.info(f"Platega webhook from IP: {client_ip}, User-Agent: {user_agent}")

            # Простая защита от флуда - ограничиваем количество запросов
            current_time = datetime.now().timestamp()
            webhook_key = f"platega_webhook_{client_ip}"

            # В реальном проекте лучше использовать Redis, но для простоты используем глобальную переменную
            if not hasattr(app, 'webhook_timestamps'):
                app.webhook_timestamps = {}

            if webhook_key in app.webhook_timestamps:
                last_request = app.webhook_timestamps[webhook_key]
                if current_time - last_request < 1:  # Не более 1 запроса в секунду с одного IP
                    logging.warning(f"Rate limit exceeded for IP: {client_ip}")
                    return "Rate limit exceeded", 429

            app.webhook_timestamps[webhook_key] = current_time

            # Проверяем заголовки для аутентификации Platega webhook
            merchant_id_header = request.headers.get('X-MerchantId')
            api_key_header = request.headers.get('X-ApiKey')

            # Если заголовки есть - проверяем их
            if merchant_id_header and api_key_header:
                if merchant_id_header != PLATEGA_MERCHANT_ID or api_key_header != PLATEGA_API_KEY:
                    logging.warning("Platega authentication failed")
                    return "Authentication failed", 401
                logging.info("Platega authentication successful via headers")
            else:
                logging.warning("Platega webhook without authentication headers - using payload validation")
            
            transaction_id = notification['id']
            payment_status = notification.get('status')
            amount = float(notification['amount'])

            # Проверка разумности суммы (защита от подделки)
            if amount < 50 or amount > 100000:
                logging.error(f"Suspicious amount in Platega webhook: {amount}")
                return "Invalid amount", 400

            # Дополнительная проверка безопасности - проверяем формат UUID
            try:
                uuid.UUID(transaction_id)
            except ValueError:
                logging.error(f"Invalid transaction_id format: {transaction_id}")
                return "Invalid transaction format", 400

            # Извлекаем user_id из payload если есть
            payload = notification.get('payload', '')
            try:
                if payload and 'platega' in payload:
                    user_id = int(payload.split('platega')[0])
                    # Дополнительная проверка - timestamp должен быть разумным
                    timestamp_part = payload.split('platega')[1]
                    timestamp = int(timestamp_part)
                    # Проверяем, что timestamp не старше 24 часов и не из будущего
                    current_time = int(datetime.now().timestamp())
                    if abs(current_time - timestamp) > 86400:  # 24 часа
                        logging.warning(f"Suspicious timestamp in payload: {timestamp}")
                else:
                    # Если payload нет, логируем ошибку
                    logging.error(f"Cannot extract user_id from Platega notification: {notification}")
                    return "Cannot identify user", 400
            except (ValueError, IndexError):
                logging.error(f"Invalid Platega payload format: {payload}")
                return "Invalid payload format", 400
            
            logging.info(f"Platega webhook: transaction_id={transaction_id}, status={payment_status}, amount={amount}, user_id={user_id}")

            if payment_status == 'CONFIRMED':
                # Дополнительная защита от дублирования - проверяем, не обрабатывали ли мы уже этот платеж
                from database import get_db_connection
                with get_db_connection() as conn:
                    c = conn.cursor()
                    # Проверяем, есть ли уже запись с таким transaction_id
                    c.execute('SELECT COUNT(*) FROM balance_history WHERE payment_method = ? AND description LIKE ?',
                             ('platega', f'%{transaction_id}%'))
                    if c.fetchone()[0] > 0:
                        logging.warning(f"Duplicate Platega payment attempt: {transaction_id}")
                        return "Payment already processed", 200

                if add_balance(user_id, amount, 'platega', 'RUB'):
                    logging.info(f"Platega payment processed for user {user_id}: {amount} RUB, transaction: {transaction_id}")
                    return "OK", 200
                else:
                    logging.warning(f"Failed to update balance for Platega payment: {transaction_id}")
                    return "Failed to update balance", 500
            else:
                logging.info(f"Platega payment status: {payment_status} for transaction {transaction_id}")
                return "OK", 200

        else:
            logging.warning("Unknown webhook format")
            return "Unknown webhook format", 400

    except json.JSONDecodeError as e:
        logging.error(f"JSON parsing error: {e}, Body: '{raw_body}'")
        return "Invalid JSON", 400
    except Exception as e:
        logging.error(f"Webhook error: {e}")
        return "Internal error", 500

# Закрытие клиента
async def shutdown():
    await HTTP_CLIENT.aclose()

# Запуск приложения
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
